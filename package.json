{"name": "astro-paper", "type": "module", "version": "5.4.2", "scripts": {"dev": "astro dev", "build": "astro check && astro build && pagefind --site dist && cp -r dist/pagefind public/", "preview": "astro preview", "sync": "astro sync", "astro": "astro", "format:check": "prettier --check .", "format": "prettier --write .", "lint": "eslint ."}, "dependencies": {"@astrojs/rss": "^4.0.12", "@astrojs/sitemap": "^3.4.1", "@resvg/resvg-js": "^2.6.2", "@tailwindcss/vite": "^4.1.8", "astro": "^5.9.2", "convex": "^1.25.0", "dayjs": "^1.11.13", "echarts": "^6.0.0", "lodash.kebabcase": "^4.1.1", "remark-collapse": "^0.1.2", "remark-toc": "^9.0.0", "satori": "^0.15.2", "sharp": "^0.34.2", "tailwindcss": "^4.1.8"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@pagefind/default-ui": "^1.3.0", "@shikijs/transformers": "^3.6.0", "@tailwindcss/typography": "^0.5.16", "@types/lodash.kebabcase": "^4.1.9", "@typescript-eslint/parser": "^8.34.0", "eslint": "^9.28.0", "eslint-plugin-astro": "^1.3.1", "globals": "^16.2.0", "pagefind": "^1.3.0", "prettier": "^3.5.3", "prettier-plugin-astro": "^0.14.1", "prettier-plugin-tailwindcss": "^0.6.12", "typescript": "^5.8.3", "typescript-eslint": "^8.34.0"}}